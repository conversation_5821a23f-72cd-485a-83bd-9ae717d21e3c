﻿using GCP.Common;
using GCP.DataAccess;
using GCP.FunctionPool;
using GCP.Functions.Common.Models;
using LinqToDB;
using System.Text.Json;

namespace GCP.Functions.Common.Services
{
    [Function("functionCode", "函数代码服务")]
    class FunctionCodeService : BaseService
    {
        [Function("getAll", "获取函数代码清单")]
        public List<FunctionCodeInfoVO> GetAll(string funcId = null)
        {
            using var db = this.GetDb();
            var data = (from a in db.LcFunctions
                        join b in db.LcFunctionCodes on a.Id equals b.FunctionId
                        where a.State == 1 && (string.IsNullOrEmpty(funcId) || a.Id == funcId)
                        select new FunctionCodeInfoVO
                        {
                            Id = a.Id,
                            SolutionId = a.SolutionId,
                            ProjectId = a.ProjectId,
                            Description = a.FunctionName,
                            UseVersion = a.UseVersion,
                            FunctionType = a.FunctionType,
                            CodeLanguage = b.CodeLanguage,
                            Code = b.Code
                        }).ToList();
            return data;
        }

        [Function("getCodeByVersion", "获取指定版本的函数代码")]
        public string GetCodeByVersion(string funcId, long? version = null)
        {
            var item = GetByVersion(funcId, version);

            //if(item.CodeLanguage == "JSON")
            //{
            //    try
            //    {
            //        var flow = JsonHelper.Deserialize<FunctionFlow>(item?.Code);
            //        return JsonHelper.Serialize(flow);
            //    }
            //    catch (Exception ex)
            //    {
            //        throw new CustomException("函数代码转换格式错误，请检查", ex);
            //    }
            //}

            return item?.Code;
        }

        private LcFunctionCode GetByVersion(string funcId, long? version = null)
        {
            using var db = this.GetDb();
            var list = (from a in db.LcFunctionCodes
                        where a.State == 1 &&
                        a.SolutionId == this.SolutionId &&
                        a.ProjectId == this.ProjectId &&
                        a.FunctionId == funcId
                        select a).ToList();

            if (list.Count == 0)
            {
                return null;
            }

            LcFunctionCode item = null;
            if (version.HasValue)
            {
                item = list.FirstOrDefault(a => a.Version == version);
                if (item == null)
                {
                    return null;
                }
            }
            else
            {
                item = list.OrderByDescending(a => a.Version).First();
            }

            return item;
        }

        [Function("saveCode", "保存函数代码")]
        public SaveCodeResult SaveCode(string funcId, string code, string codeLanguage, long? version = null, string remark = null)
        {
            using var db = this.GetDb();

            // 获取当前最新版本
            var currentVersion = GetLatestVersion(funcId);
            var newVersion = version ?? (currentVersion + 1);

            // 检查配置是否有变化
            var hasChanges = true;

            if (currentVersion > 0)
            {
                var previousCode = GetCodeByVersion(funcId, currentVersion);
                hasChanges = !string.Equals(previousCode, code, StringComparison.Ordinal);
            }

            // 如果没有变化且不是强制保存，返回结果
            if (!hasChanges && version == null)
            {
                return new SaveCodeResult
                {
                    Success = false,
                    Message = "配置未发生变化，无需保存",
                    Version = currentVersion,
                    HasChanges = false
                };
            }

            db.BeginTransaction();
            try
            {
                // 保存代码
                var item = GetByVersion(funcId, newVersion);
                if (item == null)
                {
                    item = new()
                    {
                        SolutionId = this.SolutionId,
                        ProjectId = this.ProjectId,
                        FunctionId = funcId,
                        Code = code,
                        CodeLanguage = codeLanguage,
                        Version = newVersion,
                    };
                    this.InsertData(item, db);
                }
                else
                {
                    item.Code = code;
                    this.UpdateData(item, db);
                }

                // 更新函数的使用版本
                var function = db.LcFunctions.FirstOrDefault(f => f.Id == funcId);
                if (function != null)
                {
                    function.UseVersion = newVersion;
                    this.UpdateData(function, db);
                }

                db.CommitTransaction();

                this.Context.Current.Target = item.FunctionId;
                ResiliencePipelineManager.TryRemoveByPrefix(FlowExecutor.FlowPipelineKeyPrefix + funcId);

                return new SaveCodeResult
                {
                    Success = true,
                    Message = "保存成功",
                    Version = newVersion,
                    HasChanges = hasChanges
                };
            }
            catch
            {
                db.RollbackTransaction();
                throw;
            }
        }

        [Function("getLatestVersion", "获取最新版本号")]
        public long GetLatestVersion(string funcId)
        {
            using var db = this.GetDb();
            var maxVersion = db.LcFunctionCodes
                .Where(a => a.State == 1 &&
                           a.SolutionId == this.SolutionId &&
                           a.ProjectId == this.ProjectId &&
                           a.FunctionId == funcId)
                .Max(a => (long?)a.Version);

            return maxVersion ?? 0;
        }

        [Function("getVersionLogs", "获取版本历史")]
        public List<FunctionVersionLogVO> GetVersionLogs(string funcId, int pageIndex = 1, int pageSize = 20)
        {
            using var db = this.GetDb();

            // 获取当前使用版本
            var currentUseVersion = GetCurrentUseVersion(funcId);

            var query = from code in db.LcFunctionCodes
                       where code.SolutionId == this.SolutionId &&
                             code.ProjectId == this.ProjectId &&
                             code.FunctionId == funcId &&
                             code.State == 1
                       orderby code.Version descending
                       select new FunctionVersionLogVO
                       {
                           Id = code.Id,
                           FunctionId = code.FunctionId,
                           Version = code.Version,
                           Creator = code.Creator,
                           TimeCreate = code.TimeCreate,
                           IsCurrentVersion = code.Version == currentUseVersion
                       };

            return query.Skip((pageIndex - 1) * pageSize).Take(pageSize).ToList();
        }



        [Function("rollbackToVersion", "回滚到指定版本")]
        public SaveCodeResult RollbackToVersion(string funcId, long targetVersion, string remark = null)
        {
            var targetCode = GetCodeByVersion(funcId, targetVersion);
            if (targetCode == null)
            {
                throw new CustomException("目标版本不存在");
            }

            var currentVersion = GetLatestVersion(funcId);
            var newVersion = currentVersion + 1;

            using var db = this.GetDb();
            db.BeginTransaction();
            try
            {
                // 创建新版本（回滚版本）
                var item = new LcFunctionCode
                {
                    SolutionId = this.SolutionId,
                    ProjectId = this.ProjectId,
                    FunctionId = funcId,
                    Code = targetCode,
                    CodeLanguage = "JSON", // 假设都是JSON格式
                    Version = newVersion,
                };
                this.InsertData(item, db);



                // 更新函数的使用版本
                var function = db.LcFunctions.FirstOrDefault(f => f.Id == funcId);
                if (function != null)
                {
                    function.UseVersion = newVersion;
                    this.UpdateData(function, db);
                }

                db.CommitTransaction();

                this.Context.Current.Target = item.FunctionId;
                ResiliencePipelineManager.TryRemoveByPrefix(FlowExecutor.FlowPipelineKeyPrefix + funcId);

                return new SaveCodeResult
                {
                    Success = true,
                    Message = $"已回滚到版本 {targetVersion}",
                    Version = newVersion,
                    HasChanges = true
                };
            }
            catch
            {
                db.RollbackTransaction();
                throw;
            }
        }

        [Function("publishVersion", "发布版本")]
        public PublishResult PublishVersion(string funcId, long version, string remark = null)
        {
            using var db = this.GetDb();

            // 检查版本是否存在
            var codeVersion = GetByVersion(funcId, version);
            if (codeVersion == null)
            {
                throw new CustomException("指定版本不存在");
            }

            db.BeginTransaction();
            try
            {
                // 更新函数的使用版本为发布版本
                var function = db.LcFunctions.FirstOrDefault(f => f.Id == funcId);
                if (function != null)
                {
                    function.UseVersion = version;
                    this.UpdateData(function, db);
                }



                db.CommitTransaction();

                return new PublishResult
                {
                    Success = true,
                    Message = "发布成功"
                };
            }
            catch
            {
                db.RollbackTransaction();
                throw;
            }
        }

        [Function("getCurrentUseVersion", "获取当前使用版本")]
        public long? GetCurrentUseVersion(string funcId)
        {
            using var db = this.GetDb();
            var function = db.LcFunctions.FirstOrDefault(f => f.Id == funcId);
            return function?.UseVersion;
        }
    }
}
